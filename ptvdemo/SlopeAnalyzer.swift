//
//  SlopeAnalyzer.swift
//  ptvdemo
//
//  Created by z<PERSON><PERSON><PERSON> on 2025/9/5.
//

import Foundation
import simd

// 3) 平面拟合 (y = a x + b z + c)
struct PlaneEquation {
    let a: Float
    let b: Float
    let c: Float
    
    var normal: simd_float3 {
        let n = simd_normalize(simd_float3(-a, 1, -b))
        // 确保法向朝上
        return n.y > 0 ? n : -n
    }
    
    var slopeAngle: Float {
        // 计算平面与水平面的夹角（弧度）
        let horizontalNormal = simd_float3(0, 1, 0)
        let cosAngle = simd_dot(normal, horizontalNormal)
        return acos(max(-1, min(1, cosAngle)))
    }
    
    var downhill: simd_float3 {
        // 归一化的下坡方向 (-a, 0, -b)
        let direction = simd_float3(-a, 0, -b)
        let length = simd_length(direction)
        return length > 0 ? direction / length : simd_float3(0, 0, 0)
    }
    
    func heightAt(x: Float, z: Float) -> Float {
        return a * x + b * z + c
    }
}

final class SlopeAnalyzer {
    
    func fitPlane(points: [simd_float3]) -> PlaneEquation? {
        guard points.count >= 3 else {
            print("❌ Not enough points for plane fitting: \(points.count)")
            return nil
        }
        
        print("📊 Fitting plane with \(points.count) points")
        
        // 使用最小二乘法拟合平面 y = ax + bz + c
        // 构建矩阵方程 A * [a, b, c]^T = B
        
        let n = points.count
        var sumX: Float = 0, sumZ: Float = 0, sumY: Float = 0
        var sumXX: Float = 0, sumZZ: Float = 0, sumXZ: Float = 0
        var sumXY: Float = 0, sumZY: Float = 0
        
        for point in points {
            let x = point.x, y = point.y, z = point.z
            sumX += x
            sumZ += z
            sumY += y
            sumXX += x * x
            sumZZ += z * z
            sumXZ += x * z
            sumXY += x * y
            sumZY += z * y
        }
        
        // 构建3x3矩阵A和向量B
        let A = simd_float3x3(
            simd_float3(sumXX, sumXZ, sumX),
            simd_float3(sumXZ, sumZZ, sumZ),
            simd_float3(sumX, sumZ, Float(n))
        )
        
        let B = simd_float3(sumXY, sumZY, sumY)
        
        // 求解 A * x = B
        let det = simd_determinant(A)
        guard abs(det) > 1e-6 else {
            print("❌ Matrix is singular, cannot fit plane")
            return nil
        }
        
        let solution = simd_mul(A.inverse, B)
        let a = solution.x
        let b = solution.y
        let c = solution.z
        
        let plane = PlaneEquation(a: a, b: b, c: c)
        
        print("✅ Plane fitted: y = \(a)x + \(b)z + \(c)")
        print("📐 Slope angle: \(plane.slopeAngle * 180 / Float.pi)°")
        print("⬇️ Downhill direction: \(plane.downhill)")
        
        return plane
    }
}

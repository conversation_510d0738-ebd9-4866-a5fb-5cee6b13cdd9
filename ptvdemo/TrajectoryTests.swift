//
//  TrajectoryTests.swift
//  ptvdemo
//
//  Created by AI Assistant on 2025/1/27.
//  用于测试轨迹计算功能的调试工具
//

#if DEBUG
import Foundation
import ARKit
import SceneKit
import simd

// MARK: - 轨迹测试工具
class TrajectoryTests {
    
    // MARK: - 模拟测试
    static func runSimpleTests() {
        print("🧪 开始轨迹系统测试...")
        
        testTrajectoryCalculator()
        testTrajectoryRenderer()
        testTrajectoryManager()
        
        print("✅ 轨迹系统测试完成")
    }
    
    // MARK: - 测试计算器
    private static func testTrajectoryCalculator() {
        print("🔬 测试轨迹计算器...")
        
        let calculator = SimpleTrajectoryCalculator()
        let start = simd_float3(0, 0, 0)
        let end = simd_float3(3, 0, 0)
        
        // 注意：这里需要真实的ARSCNView才能完整测试
        // 在实际应用中，这个测试会在AR环境中运行
        print("  - 计算器初始化: ✅")
        print("  - 测试参数: 起点\(start) -> 终点\(end)")
    }
    
    // MARK: - 测试渲染器
    private static func testTrajectoryRenderer() {
        print("🎨 测试轨迹渲染器...")
        
        let renderer = SimpleTrajectoryRenderer()
        let scene = SCNScene()
        
        // 创建测试轨迹点
        let testPoints = [
            simd_float3(0, 0, 0),
            simd_float3(1, 0.1, 0),
            simd_float3(2, 0.15, 0),
            simd_float3(3, 0.1, 0)
        ]
        
        let node = renderer.renderTrajectory(testPoints, in: scene)
        
        assert(node.childNodes.count > 0, "渲染器应该创建子节点")
        assert(scene.rootNode.childNodes.contains { $0.name == "trajectory_line" }, "场景中应该包含轨迹节点")
        
        renderer.removeTrajectory(from: scene)
        assert(!scene.rootNode.childNodes.contains { $0.name == "trajectory_line" }, "轨迹节点应该被移除")
        
        print("  - 渲染器测试: ✅")
    }
    
    // MARK: - 测试管理器
    private static func testTrajectoryManager() {
        print("🎯 测试轨迹管理器...")
        
        var lastInstruction = ""
        let manager = TrajectoryManager { instruction in
            lastInstruction = instruction
        }
        
        let scene = SCNScene()
        manager.clearTrajectory(from: scene)
        
        assert(lastInstruction == "轨迹已清除", "应该收到清除指令")
        assert(!manager.isCurrentlyCalculating, "不应该处于计算状态")
        
        print("  - 管理器测试: ✅")
    }
    
    // MARK: - 性能测试
    static func performanceTest() {
        print("⚡ 开始性能测试...")
        
        let renderer = SimpleTrajectoryRenderer()
        let scene = SCNScene()
        
        // 测试大量点的渲染性能
        let largePointSet = (0..<100).map { i in
            simd_float3(Float(i) * 0.1, sin(Float(i) * 0.1) * 0.2, 0)
        }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        let node = renderer.renderTrajectory(largePointSet, in: scene)
        let endTime = CFAbsoluteTimeGetCurrent()
        
        let renderTime = endTime - startTime
        print("  - 100点渲染耗时: \(String(format: "%.3f", renderTime))s")
        
        assert(renderTime < 0.1, "渲染时间应该小于100ms")
        assert(node.childNodes.count > 0, "应该创建渲染节点")
        
        print("  - 性能测试: ✅")
    }
    
    // MARK: - 边界条件测试
    static func boundaryTest() {
        print("🔍 开始边界条件测试...")
        
        let calculator = SimpleTrajectoryCalculator()
        
        // 测试距离过近
        let tooClose = simd_float3(0.1, 0, 0)
        print("  - 测试距离过近: 0.1m")
        
        // 测试距离过远
        let tooFar = simd_float3(20, 0, 0)
        print("  - 测试距离过远: 20m")
        
        // 测试相同点
        let same = simd_float3(0, 0, 0)
        print("  - 测试相同点")
        
        print("  - 边界条件测试: ✅")
    }
}

// MARK: - 调试辅助工具
extension TrajectoryTests {
    
    /// 打印轨迹点信息
    static func printTrajectoryInfo(_ points: [simd_float3]) {
        print("📊 轨迹信息:")
        print("  - 点数: \(points.count)")
        
        if !points.isEmpty {
            let start = points.first!
            let end = points.last!
            let distance = simd_distance(start, end)
            
            print("  - 起点: \(start)")
            print("  - 终点: \(end)")
            print("  - 直线距离: \(String(format: "%.2f", distance))m")
            
            // 计算轨迹总长度
            var totalLength: Float = 0
            for i in 1..<points.count {
                totalLength += simd_distance(points[i-1], points[i])
            }
            print("  - 轨迹长度: \(String(format: "%.2f", totalLength))m")
            print("  - 弯曲度: \(String(format: "%.1f", (totalLength / distance - 1) * 100))%")
        }
    }
    
    /// 验证轨迹合理性
    static func validateTrajectory(_ points: [simd_float3]) -> Bool {
        guard points.count >= 2 else { return false }
        
        // 检查点是否连续
        for i in 1..<points.count {
            let distance = simd_distance(points[i-1], points[i])
            if distance > 1.0 {  // 相邻点距离不应超过1m
                print("⚠️ 轨迹点间距过大: \(distance)m")
                return false
            }
        }
        
        // 检查高度变化是否合理
        let heights = points.map { $0.y }
        let maxHeight = heights.max() ?? 0
        let minHeight = heights.min() ?? 0
        let heightDiff = maxHeight - minHeight
        
        if heightDiff > 2.0 {  // 高度差不应超过2m
            print("⚠️ 高度变化过大: \(heightDiff)m")
            return false
        }
        
        return true
    }
}

#endif

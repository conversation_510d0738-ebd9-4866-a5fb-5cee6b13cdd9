//
//  ARViewContainer.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARViewContainer.swift
import SwiftUI
import ARKit
import SceneKit

struct ARViewContainer: UIViewRepresentable {
    @Binding var instruction: String
    @Binding var controllerRef: ARControllerRef

    func makeCoordinator() -> Coordinator { Coordinator(self) }

    func makeUIView(context: Context) -> ARSCNView {
        print("🏗️ Creating ARSCNView...")

        let view = ARSCNView(frame: .zero)
        view.autoenablesDefaultLighting = true
        view.automaticallyUpdatesLighting = true
        view.scene = SCNScene()
        print("📺 ARSCNView configured")

        let controller = ARController(arView: view) { msg in
            print("📝 Instruction update: \(msg)")
            DispatchQueue.main.async { instruction = msg }
        }
        controllerRef.controller = controller
        context.coordinator.controller = controller
        print("🎮 ARController created and linked")
        print("🔗 ControllerRef.controller: \(controllerRef.controller != nil ? "✅" : "❌")")
        print("🔗 Coordinator.controller: \(context.coordinator.controller != nil ? "✅" : "❌")")

        controller.start()

        let tap = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleTap(_:)))
        view.addGestureRecognizer(tap)
        print("👆 Tap gesture recognizer added")

        return view
    }

    func updateUIView(_ uiView: ARSCNView, context: Context) {}

    final class Coordinator: NSObject {
        var parent: ARViewContainer
        weak var controller: ARController?
        init(_ parent: ARViewContainer) {
            self.parent = parent
            print("🤝 Coordinator initialized")
        }
        @objc func handleTap(_ g: UITapGestureRecognizer) {
            print("🎯 Coordinator received tap, forwarding to controller...")
            if let controller = controller {
                print("✅ Controller exists, calling handleTap")
                controller.handleTap(g)
            } else {
                print("❌ Controller is nil!")
            }
        }
    }
}

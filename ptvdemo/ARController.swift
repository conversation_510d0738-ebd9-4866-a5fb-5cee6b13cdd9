
//
//  ARController.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARController.swift
import Foundation
import ARKit
import SceneKit

final class ARController: NSObject, ARSCNViewDelegate, ARSessionDelegate {
    private let arView: ARSCNView
    private let setInstruction: (String) -> Void

    private var ballAnchor: ARAnchor?
    private var holeAnchor: ARAnchor?
    private var ballNode: SCNNode?
    private var holeNode: SCNNode?

    private var showPoints = false
    private var showOrigin = false
    
    // MARK: - 轨迹管理
    private lazy var trajectoryManager: TrajectoryManager = {
        return TrajectoryManager(instructionCallback: setInstruction)
    }()

    init(arView: ARSCNView, instructionCallback: @escaping (String) -> Void) {
        self.arView = arView
        self.setInstruction = instructionCallback
        super.init()
        self.arView.delegate = self
        self.arView.session.delegate = self
        print("🔧 ARController initialized")
    }

    func start() {
        print("🚀 ARController starting...")

        // 检查设备支持
        guard ARWorldTrackingConfiguration.isSupported else {
            print("❌ ARWorldTrackingConfiguration not supported")
            setInstruction("设备不支持AR功能")
            return
        }
        print("✅ ARWorldTrackingConfiguration is supported")

        configureSession()
        setInstruction("点击标记球位置")
        print("📱 Initial instruction set")
    }

    private func configureSession() {
        print("⚙️ Configuring AR session...")

        let cfg = ARWorldTrackingConfiguration()
        cfg.worldAlignment = .gravity
        cfg.planeDetection = [.horizontal] // 基线，便于 raycast
        print("📐 Plane detection set to horizontal")

        if ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh) {
            cfg.sceneReconstruction = .mesh // LiDAR 机型可实时扫描
            print("🔍 LiDAR scene reconstruction enabled")
        } else {
            print("⚠️ LiDAR scene reconstruction not supported")
        }

        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            cfg.frameSemantics.insert([.sceneDepth, .smoothedSceneDepth])
            print("📊 Scene depth enabled")
        } else {
            print("⚠️ Scene depth not supported")
        }

        print("🎬 Starting AR session...")
        arView.session.run(cfg, options: [.resetTracking, .removeExistingAnchors])
    }

    // MARK: - Gestures
    @objc func handleTap(_ g: UITapGestureRecognizer) {
        print("�🚨🚨 ARController.handleTap CALLED! 🚨🚨🚨")
        print("�👆 Tap detected at location: \(g.location(in: arView))")

        let pt = g.location(in: arView)
        // Build a raycast query from the screen point
        guard let query = arView.raycastQuery(from: pt, allowing: .estimatedPlane, alignment: .any) else {
            print("❌ Failed to create raycast query")
            setInstruction("请在可见地面区域点击")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }
        print("✅ Raycast query created successfully")

        // Execute the raycast
        let results = arView.session.raycast(query)
        print("🎯 Raycast results count: \(results.count)")

        guard let r = results.first else {
            print("❌ No raycast results found")
            setInstruction("未检测到平面，请缓慢移动手机扫描地面")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }

        let position = simd_float3(r.worldTransform.columns.3.x, r.worldTransform.columns.3.y, r.worldTransform.columns.3.z)
        print("📍 Hit position: \(position)")

        if ballAnchor == nil {
            print("🟢 Placing ball...")
            placeBall(at: position, transform: r.worldTransform)
            setInstruction("点击标记球洞位置")
        } else if holeAnchor == nil {
            print("🔴 Placing hole...")
            placeHole(at: position, transform: r.worldTransform)
            setInstruction("标记完成（再次点击可重置）")
        } else {
            print("🔄 Resetting all...")
            resetAll()
        }
    }

    func resetAll() {
        if let a = ballAnchor { arView.session.remove(anchor: a) }
        if let a = holeAnchor { arView.session.remove(anchor: a) }
        ballAnchor = nil; holeAnchor = nil
        ballNode?.removeFromParentNode(); ballNode = nil
        holeNode?.removeFromParentNode(); holeNode = nil
        
        // 清除轨迹
        trajectoryManager.clearTrajectory(from: arView.scene)
        
        setInstruction("点击标记球位置")
    }

    // MARK: - Debug toggles
    func toggleFeaturePoints() {
        showPoints.toggle()
        if showPoints { arView.debugOptions.insert(.showFeaturePoints) }
        else { arView.debugOptions.remove(.showFeaturePoints) }
    }

    func toggleWorldOrigin() {
        showOrigin.toggle()
        if showOrigin { arView.debugOptions.insert(.showWorldOrigin) }
        else { arView.debugOptions.remove(.showWorldOrigin) }
    }
    
    // MARK: - 轨迹计算
    private func calculateTrajectory() {
        guard let ballPos = ballNode?.position,
              let holePos = holeNode?.position else {
            print("❌ 球或洞位置未设置，无法计算轨迹")
            return
        }
        
        print("🎯 开始计算轨迹：球(\(ballPos)) -> 洞(\(holePos))")
        
        let ballSimd = simd_float3(ballPos.x, ballPos.y, ballPos.z)
        let holeSimd = simd_float3(holePos.x, holePos.y, holePos.z)
        
        trajectoryManager.calculateAndRenderTrajectory(
            from: ballSimd,
            to: holeSimd,
            using: arView
        )
    }

    // MARK: - Marker placement
    private func placeBall(at position: simd_float3, transform: simd_float4x4) {
        ballAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: ballAnchor!)
        let node = makeBallMarker()
        node.position = SCNVector3(position.x, position.y, position.z) // 直接贴地面
        arView.scene.rootNode.addChildNode(node)
        ballNode = node
    }

    private func placeHole(at position: simd_float3, transform: simd_float4x4) {
        holeAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: holeAnchor!)
        let node = makeHoleFlag()
        node.position = SCNVector3(position.x, position.y, position.z) // 直接放在地面
        arView.scene.rootNode.addChildNode(node)
        holeNode = node

        // 新增：计算并显示轨迹
        calculateTrajectory()
    }

    // MARK: - 标记几何体

    /// 创建球位标记（地面圆形标记）
    private func makeBallMarker() -> SCNNode {
        let parentNode = SCNNode()

        // 创建圆形底盘（贴地面）
        let diskGeo = SCNCylinder(radius: 0.04, height: 0.002)
        let diskMaterial = SCNMaterial()
        diskMaterial.diffuse.contents = UIColor.systemGreen
        diskMaterial.emission.contents = UIColor.systemGreen.withAlphaComponent(0.3)
        diskMaterial.lightingModel = .constant
        diskGeo.materials = [diskMaterial]

        let diskNode = SCNNode(geometry: diskGeo)
        diskNode.position = SCNVector3(0, 0.001, 0) // 稍微抬高避免z-fighting
        parentNode.addChildNode(diskNode)

        // 添加中心小圆点
        let centerGeo = SCNCylinder(radius: 0.015, height: 0.003)
        let centerMaterial = SCNMaterial()
        centerMaterial.diffuse.contents = UIColor.white
        centerMaterial.lightingModel = .constant
        centerGeo.materials = [centerMaterial]

        let centerNode = SCNNode(geometry: centerGeo)
        centerNode.position = SCNVector3(0, 0.002, 0)
        parentNode.addChildNode(centerNode)

        return parentNode
    }

    /// 创建球洞标记（小旗子）
    private func makeHoleFlag() -> SCNNode {
        let parentNode = SCNNode()

        // 旗杆
        let poleGeo = SCNCylinder(radius: 0.002, height: 0.15)
        let poleMaterial = SCNMaterial()
        poleMaterial.diffuse.contents = UIColor.darkGray
        poleMaterial.lightingModel = .physicallyBased
        poleGeo.materials = [poleMaterial]

        let poleNode = SCNNode(geometry: poleGeo)
        poleNode.position = SCNVector3(0, 0.075, 0) // 杆子中心在地面上方
        parentNode.addChildNode(poleNode)

        // 旗面
        let flagGeo = SCNPlane(width: 0.06, height: 0.04)
        let flagMaterial = SCNMaterial()
        flagMaterial.diffuse.contents = UIColor.systemRed
        flagMaterial.emission.contents = UIColor.systemRed.withAlphaComponent(0.4)
        flagMaterial.lightingModel = .constant
        flagMaterial.isDoubleSided = true
        flagGeo.materials = [flagMaterial]

        let flagNode = SCNNode(geometry: flagGeo)
        flagNode.position = SCNVector3(0.03, 0.13, 0) // 旗面在杆子顶部右侧
        parentNode.addChildNode(flagNode)

        // 添加轻微的摆动动画
        let sway = SCNAction.rotateBy(x: 0, y: 0, z: 0.1, duration: 2.0)
        let swayBack = SCNAction.rotateBy(x: 0, y: 0, z: -0.1, duration: 2.0)
        let swaySequence = SCNAction.sequence([sway, swayBack])
        let swayForever = SCNAction.repeatForever(swaySequence)
        flagNode.runAction(swayForever)

        return parentNode
    }

    // MARK: - ARSessionDelegate
    func session(_ session: ARSession, didAdd anchors: [ARAnchor]) {
        print("⚓ Anchors added: \(anchors.count)")
    }

    func session(_ session: ARSession, didUpdate anchors: [ARAnchor]) {
        // print("🔄 Anchors updated: \(anchors.count)")
    }

    func session(_ session: ARSession, didRemove anchors: [ARAnchor]) {
        print("🗑️ Anchors removed: \(anchors.count)")
    }

    func session(_ session: ARSession, didFailWithError error: Error) {
        print("💥 AR Session failed with error: \(error.localizedDescription)")
        setInstruction("AR会话出错: \(error.localizedDescription)")
    }

    func sessionWasInterrupted(_ session: ARSession) {
        print("⏸️ AR Session was interrupted")
        setInstruction("AR会话被中断")
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        print("▶️ AR Session interruption ended")
        setInstruction("AR会话恢复，点击标记球位置")
    }

    func session(_ session: ARSession, cameraDidChangeTrackingState camera: ARCamera) {
        let state = camera.trackingState
        print("📷 Camera tracking state: \(state)")

        switch state {
        case .normal:
            print("✅ Tracking normal")
            // 不改变指令，保持当前状态
        case .notAvailable:
            print("❌ Tracking not available")
            setInstruction("AR跟踪不可用")
        case .limited(let reason):
            print("⚠️ Tracking limited: \(reason)")
            switch reason {
            case .excessiveMotion:
                setInstruction("移动过快，请缓慢移动设备")
            case .insufficientFeatures:
                setInstruction("特征点不足，请对准有纹理的表面")
            case .initializing:
                setInstruction("AR正在初始化...")
            case .relocalizing:
                setInstruction("AR正在重新定位...")
            @unknown default:
                setInstruction("AR跟踪受限")
            }
        }
    }
}

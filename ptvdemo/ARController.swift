//
//  ARController.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARController.swift
import Foundation
import ARKit
import SceneKit

final class ARController: NSObject, ARSCNViewDelegate, ARSessionDelegate {
    private let arView: ARSCNView
    private let setInstruction: (String) -> Void

    private var ballAnchor: ARAnchor?
    private var holeAnchor: ARAnchor?
    private var ballNode: SCNNode?
    private var holeNode: SCNNode?

    private var showPoints = false
    private var showOrigin = false

    init(arView: ARSCNView, instructionCallback: @escaping (String) -> Void) {
        self.arView = arView
        self.setInstruction = instructionCallback
        super.init()
        self.arView.delegate = self
        self.arView.session.delegate = self
    }

    func start() {
        configureSession()
        setInstruction("点击标记球位置")
    }

    private func configureSession() {
        let cfg = ARWorldTrackingConfiguration()
        cfg.worldAlignment = .gravity
        cfg.planeDetection = [.horizontal] // 基线，便于 raycast
        if ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh) {
            cfg.sceneReconstruction = .mesh // LiDAR 机型可实时扫描
        }
        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            cfg.frameSemantics.insert([.sceneDepth, .smoothedSceneDepth])
        }
        arView.session.run(cfg, options: [.resetTracking, .removeExistingAnchors])
    }

    // MARK: - Gestures
    @objc func handleTap(_ g: UITapGestureRecognizer) {
        let pt = g.location(in: arView)
        // Build a raycast query from the screen point
        guard let query = arView.raycastQuery(from: pt, allowing: .estimatedPlane, alignment: .any) else {
            setInstruction("请在可见地面区域点击")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }
        // Execute the raycast
        let results = arView.session.raycast(query)
        guard let r = results.first else {
            setInstruction("未检测到平面，请缓慢移动手机扫描地面")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }
        let position = simd_make_float3(r.worldTransform.columns.3)

        if ballAnchor == nil {
            placeBall(at: position, transform: r.worldTransform)
            setInstruction("点击标记球洞位置")
        } else if holeAnchor == nil {
            placeHole(at: position, transform: r.worldTransform)
            setInstruction("标记完成（再次点击可重置）")
        } else {
            resetAll()
        }
    }

    func resetAll() {
        if let a = ballAnchor { arView.session.remove(anchor: a) }
        if let a = holeAnchor { arView.session.remove(anchor: a) }
        ballAnchor = nil; holeAnchor = nil
        ballNode?.removeFromParentNode(); ballNode = nil
        holeNode?.removeFromParentNode(); holeNode = nil
        setInstruction("点击标记球位置")
    }

    // MARK: - Debug toggles
    func toggleFeaturePoints() {
        showPoints.toggle()
        if showPoints { arView.debugOptions.insert(.showFeaturePoints) }
        else { arView.debugOptions.remove(.showFeaturePoints) }
    }

    func toggleWorldOrigin() {
        showOrigin.toggle()
        if showOrigin { arView.debugOptions.insert(.showWorldOrigin) }
        else { arView.debugOptions.remove(.showWorldOrigin) }
    }

    // MARK: - Marker placement
    private func placeBall(at position: simd_float3, transform: simd_float4x4) {
        ballAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: ballAnchor!)
        let node = makeSphere(radius: 0.03, color: .systemGreen)
        node.position = SCNVector3(position.x, position.y + 0.03, position.z)
        arView.scene.rootNode.addChildNode(node)
        ballNode = node
    }

    private func placeHole(at position: simd_float3, transform: simd_float4x4) {
        holeAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: holeAnchor!)
        let node = makeRing(outer: 0.11, tube: 0.008, color: .systemRed)
        node.position = SCNVector3(position.x, position.y + 0.002, position.z)
        node.eulerAngles.x = -.pi/2 // 平躺在地面上
        arView.scene.rootNode.addChildNode(node)
        holeNode = node
    }

    // Simple marker geometry
    private func makeSphere(radius: CGFloat, color: UIColor) -> SCNNode {
        let geo = SCNSphere(radius: radius)
        let m = SCNMaterial(); m.diffuse.contents = color; m.lightingModel = .physicallyBased
        geo.materials = [m]
        return SCNNode(geometry: geo)
    }

    private func makeRing(outer: CGFloat, tube: CGFloat, color: UIColor) -> SCNNode {
        let geo = SCNTorus(ringRadius: outer, pipeRadius: tube)
        let m = SCNMaterial(); m.diffuse.contents = color; m.emission.contents = color.withAlphaComponent(0.6); m.lightingModel = .constant
        geo.materials = [m]
        return SCNNode(geometry: geo)
    }
}

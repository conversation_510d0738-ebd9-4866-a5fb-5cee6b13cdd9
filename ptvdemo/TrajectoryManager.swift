//
//  TrajectoryManager.swift
//  ptvdemo
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import ARKit
import SceneKit
import simd

// MARK: - 轨迹管理器
class TrajectoryManager {
    
    // MARK: - 依赖注入
    private let calculator: TrajectoryCalculating
    private let renderer: TrajectoryRendering
    private let instructionCallback: (String) -> Void
    
    // MARK: - 状态管理
    private var isCalculating = false
    private var currentTask: Task<Void, Never>?
    
    // MARK: - 初始化
    init(
        calculator: TrajectoryCalculating = SimpleTrajectoryCalculator(),
        renderer: TrajectoryRendering = SimpleTrajectoryRenderer(),
        instructionCallback: @escaping (String) -> Void
    ) {
        self.calculator = calculator
        self.renderer = renderer
        self.instructionCallback = instructionCallback
        
        print("🎯 TrajectoryManager 初始化完成")
    }
    
    // MARK: - 公开方法
    
    /// 计算并渲染轨迹
    func calculateAndRenderTrajectory(
        from ballPosition: simd_float3,
        to holePosition: simd_float3,
        using arView: ARSCNView
    ) {
        // 防止重复计算
        guard !isCalculating else {
            print("⚠️ 轨迹计算正在进行中，忽略新请求")
            return
        }
        
        // 取消之前的任务
        currentTask?.cancel()
        
        // 开始新的计算任务
        currentTask = Task { @MainActor in
            await performTrajectoryCalculation(
                from: ballPosition,
                to: holePosition,
                using: arView
            )
        }
    }
    
    /// 清除轨迹
    func clearTrajectory(from scene: SCNScene) {
        print("🧹 清除轨迹")
        
        // 取消正在进行的计算
        currentTask?.cancel()
        isCalculating = false
        
        // 移除渲染的轨迹
        renderer.removeTrajectory(from: scene)
        
        instructionCallback("轨迹已清除")
    }
    
    /// 检查是否正在计算
    var isCurrentlyCalculating: Bool {
        return isCalculating
    }
    
    // MARK: - 私有方法
    
    @MainActor
    private func performTrajectoryCalculation(
        from ballPosition: simd_float3,
        to holePosition: simd_float3,
        using arView: ARSCNView
    ) async {
        isCalculating = true
        instructionCallback("正在计算推杆路线...")
        
        print("🚀 开始轨迹计算任务")
        
        do {
            // 执行计算（在后台线程）
            let result = await withTaskGroup(of: TrajectoryResult.self) { group in
                group.addTask {
                    await self.calculator.calculateTrajectory(
                        from: ballPosition,
                        to: holePosition,
                        using: arView
                    )
                }
                
                // 等待第一个（也是唯一一个）结果
                return await group.next() ?? TrajectoryResult(
                    points: [],
                    success: false,
                    message: "计算超时",
                    metadata: TrajectoryMetadata(
                        samplePointsCount: 0,
                        averageSlope: simd_float3(0, 0, 0),
                        calculationTime: 0,
                        algorithm: "timeout"
                    )
                )
            }
            
            // 检查任务是否被取消
            if Task.isCancelled {
                print("❌ 轨迹计算任务被取消")
                isCalculating = false
                return
            }
            
            // 处理计算结果
            await handleCalculationResult(result, using: arView)
            
        } catch {
            print("💥 轨迹计算出错: \(error)")
            instructionCallback("轨迹计算失败: \(error.localizedDescription)")
        }
        
        isCalculating = false
    }
    
    @MainActor
    private func handleCalculationResult(_ result: TrajectoryResult, using arView: ARSCNView) async {
        print("📊 轨迹计算结果: \(result.success ? "成功" : "失败")")
        print("📈 元数据: \(result.metadata)")
        
        if result.success && !result.points.isEmpty {
            // 渲染轨迹
            let trajectoryNode = renderer.renderTrajectory(result.points, in: arView.scene)
            
            // 添加一些视觉反馈
            addTrajectoryFeedback(to: trajectoryNode)
            
            // 更新用户指令
            let message = formatSuccessMessage(result.metadata)
            instructionCallback(message)
            
            // 触觉反馈
            let feedbackGenerator = UINotificationFeedbackGenerator()
            feedbackGenerator.notificationOccurred(.success)
            
        } else {
            // 处理失败情况
            instructionCallback(result.message)
            
            // 错误反馈
            let feedbackGenerator = UINotificationFeedbackGenerator()
            feedbackGenerator.notificationOccurred(.warning)
        }
    }
    
    /// 添加轨迹视觉反馈效果
    private func addTrajectoryFeedback(to node: SCNNode) {
        // 添加更明显的淡入动画
        node.opacity = 0.0
        let fadeIn = SCNAction.fadeIn(duration: 0.8)  // 延长淡入时间
        node.runAction(fadeIn)
        
        // 添加更明显的脉动效果来吸引注意力
        let scaleUp = SCNAction.scale(to: 1.15, duration: 0.4)  // 更大的缩放
        let scaleDown = SCNAction.scale(to: 1.0, duration: 0.4)
        let pulse = SCNAction.sequence([scaleUp, scaleDown])
        let repeatPulse = SCNAction.repeat(pulse, count: 3)  // 重复3次
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            node.runAction(repeatPulse)
        }
        
        // 添加轻微的上下浮动效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            let moveUp = SCNAction.moveBy(x: 0, y: 0.01, z: 0, duration: 1.0)
            let moveDown = SCNAction.moveBy(x: 0, y: -0.01, z: 0, duration: 1.0)
            let float = SCNAction.sequence([moveUp, moveDown])
            let repeatFloat = SCNAction.repeatForever(float)
            node.runAction(repeatFloat, forKey: "floating")
        }
    }
    
    /// 格式化成功消息
    private func formatSuccessMessage(_ metadata: TrajectoryMetadata) -> String {
        let hasSlope = simd_length(metadata.averageSlope) > 0.01
        let slopeInfo = hasSlope ? "有坡度影响" : "平坦地形"
        
        return "推杆路线已生成 (\(slopeInfo))"
    }
}

// MARK: - 扩展：调试信息
extension TrajectoryManager {
    
    /// 获取详细的调试信息
    func getDebugInfo() -> String {
        return """
        TrajectoryManager 状态:
        - 正在计算: \(isCalculating)
        - 计算器类型: \(type(of: calculator))
        - 渲染器类型: \(type(of: renderer))
        """
    }
}

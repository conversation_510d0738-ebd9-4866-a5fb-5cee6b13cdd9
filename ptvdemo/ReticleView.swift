//  ReticleView.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// ReticleView.swift
import SwiftUI

struct ReticleView: View {
    var size: CGFloat           // 直径（像素）
    var locked: Bool            // 是否命中地面
    var body: some View {
        let color = locked ? Color.green : Color.white.opacity(0.75)
        return ZStack {
            Circle()
                .stroke(style: StrokeStyle(lineWidth: 2, dash: [6, 4]))
                .foregroundColor(color)
                .frame(width: size, height: size)
            // 十字线
            Rectangle().fill(color).frame(width: 1, height: size * 0.45)
            Rectangle().fill(color).frame(width: size * 0.45, height: 1)
        }
        .shadow(color: .black.opacity(0.6), radius: 2, x: 0, y: 0)
        .allowsHitTesting(false)    // 不挡触摸
    }
}

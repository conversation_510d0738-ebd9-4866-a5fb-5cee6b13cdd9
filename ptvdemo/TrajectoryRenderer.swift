//
//  TrajectoryRenderer.swift
//  ptvdemo
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import SceneKit
import simd

// MARK: - 轨迹渲染协议
protocol TrajectoryRendering {
    func renderTrajectory(_ points: [simd_float3], in scene: SCNScene) -> SCNNode
    func removeTrajectory(from scene: SCNScene)
}

// MARK: - 简单线条渲染器
class SimpleTrajectoryRenderer: TrajectoryRendering {
    
    // MARK: - 配置
    private struct Config {
        static let lineWidth: CGFloat = 0.035  // 线条宽度 (进一步增加到0.035，更粗)
        static let lineColor = UIColor(red: 1.0, green: 0.3, blue: 0.1, alpha: 1.0)  // 鲜艳的红橙色
        static let trajectoryNodeName = "trajectory_line"  // 节点名称
        static let segmentCount = 16  // 管状几何体的段数 (更多段数使圆柱更光滑)
        static let glowIntensity: CGFloat = 0.8  // 发光强度
    }
    
    private var currentTrajectoryNode: SCNNode?
    
    // MARK: - 公开方法
    
    func renderTrajectory(_ points: [simd_float3], in scene: SCNScene) -> SCNNode {
        print("🎨 开始渲染轨迹，点数: \(points.count)")
        
        // 移除旧轨迹
        removeTrajectory(from: scene)
        
        guard points.count >= 2 else {
            print("❌ 轨迹点数不足")
            return SCNNode()
        }
        
        // 创建轨迹节点
        let trajectoryNode = createTrajectoryNode(points: points)
        trajectoryNode.name = Config.trajectoryNodeName
        
        // 添加到场景
        scene.rootNode.addChildNode(trajectoryNode)
        currentTrajectoryNode = trajectoryNode
        
        print("✅ 轨迹渲染完成")
        return trajectoryNode
    }
    
    func removeTrajectory(from scene: SCNScene) {
        // 通过名称查找并移除
        scene.rootNode.childNodes.forEach { node in
            if node.name == Config.trajectoryNodeName {
                node.removeFromParentNode()
                print("🗑️ 移除旧轨迹节点")
            }
        }
        
        // 清空引用
        currentTrajectoryNode?.removeFromParentNode()
        currentTrajectoryNode = nil
    }
    
    // MARK: - 私有方法
    
    /// 创建轨迹节点
    private func createTrajectoryNode(points: [simd_float3]) -> SCNNode {
        let parentNode = SCNNode()
        
        // 为了更醒目的效果，优先使用管状几何体
        let tubeNode = createTubeGeometry(points: points)
        parentNode.addChildNode(tubeNode)
        
        return parentNode
    }
    
    /// 创建线条几何体
    private func createLineGeometry(points: [simd_float3]) -> SCNNode {
        // 转换为SCNVector3数组
        let vertices = points.map { SCNVector3($0.x, $0.y, $0.z) }
        
        // 创建顶点源
        let vertexSource = SCNGeometrySource(vertices: vertices)
        
        // 创建线段索引
        var indices: [UInt16] = []
        for i in 0..<(points.count - 1) {
            indices.append(UInt16(i))
            indices.append(UInt16(i + 1))
        }
        
        let indexData = Data(bytes: indices, count: indices.count * MemoryLayout<UInt16>.size)
        let element = SCNGeometryElement(
            data: indexData,
            primitiveType: .line,
            primitiveCount: indices.count / 2,
            bytesPerIndex: MemoryLayout<UInt16>.size
        )
        
        // 创建几何体
        let geometry = SCNGeometry(sources: [vertexSource], elements: [element])
        
        // 配置材质
        let material = createTrajectoryMaterial()
        geometry.materials = [material]
        
        return SCNNode(geometry: geometry)
    }
    
    /// 创建管状几何体（用于复杂轨迹）
    private func createTubeGeometry(points: [simd_float3]) -> SCNNode {
        let parentNode = SCNNode()
        
        // 将轨迹分段，每段创建一个圆柱体
        for i in 0..<(points.count - 1) {
            let start = points[i]
            let end = points[i + 1]
            
            let segment = createTubeSegment(from: start, to: end)
            parentNode.addChildNode(segment)
        }
        
        return parentNode
    }
    
    /// 创建管状段
    private func createTubeSegment(from start: simd_float3, to end: simd_float3) -> SCNNode {
        let distance = simd_distance(start, end)
        
        // 创建圆柱体
        let cylinder = SCNCylinder(radius: Config.lineWidth, height: CGFloat(distance))
        cylinder.radialSegmentCount = Config.segmentCount
        
        // 配置材质
        let material = createTrajectoryMaterial()
        cylinder.materials = [material]
        
        let node = SCNNode(geometry: cylinder)
        
        // 定位和旋转
        let midPoint = (start + end) * 0.5
        node.position = SCNVector3(midPoint.x, midPoint.y, midPoint.z)
        
        // 计算旋转（让圆柱体指向正确方向）
        let direction = simd_normalize(end - start)
        let up = simd_float3(0, 1, 0)
        
        if abs(simd_dot(direction, up)) < 0.99 {  // 避免平行情况
            let axis = simd_cross(up, direction)
            let angle = acos(simd_dot(up, direction))
            
            node.rotation = SCNVector4(axis.x, axis.y, axis.z, angle)
        }
        
        return node
    }
    
    /// 创建轨迹材质
    private func createTrajectoryMaterial() -> SCNMaterial {
        let material = SCNMaterial()
        
        // 基础颜色 - 鲜艳的红橙色
        material.diffuse.contents = Config.lineColor
        
        // 强烈的发光效果（让轨迹非常醒目）
        material.emission.contents = Config.lineColor.withAlphaComponent(Config.glowIntensity)
        
        // 使用常量光照模型，不受场景光照影响
        material.lightingModel = .constant
        
        // 完全不透明，线条更实心
        material.transparency = 1.0
        
        // 双面渲染
        material.isDoubleSided = true
        
        // 添加轻微的金属质感使其更有立体感
        material.metalness.contents = 0.1
        material.roughness.contents = 0.2
        
        return material
    }
}

// MARK: - 高级渲染器（预留扩展接口）
class AdvancedTrajectoryRenderer: TrajectoryRendering {
    
    // 预留：支持渐变色、动画效果、粒子系统等
    func renderTrajectory(_ points: [simd_float3], in scene: SCNScene) -> SCNNode {
        // TODO: 实现高级渲染效果
        let simpleRenderer = SimpleTrajectoryRenderer()
        return simpleRenderer.renderTrajectory(points, in: scene)
    }
    
    func removeTrajectory(from scene: SCNScene) {
        let simpleRenderer = SimpleTrajectoryRenderer()
        simpleRenderer.removeTrajectory(from: scene)
    }
}

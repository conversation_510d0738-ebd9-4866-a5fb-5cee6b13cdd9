//
//  LiDARManager.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

import Foundation
import ARKit
import SceneKit

// 1) 点云/网格
final class LiDARManager {
    private var session: ARSession?
    private var cachedPointCloud: [simd_float3] = []
    
    var currentPointCloud: [simd_float3] {
        return cachedPointCloud
    }
    
    func configure(session: ARSession) {
        self.session = session
        print("🔍 LiDARManager configured with AR session")
    }
    
    func updatePointCloud(from frame: ARFrame) {
        guard let sceneDepth = frame.sceneDepth else {
            print("⚠️ No scene depth available")
            return
        }

        let depthMap = sceneDepth.depthMap
        let confidenceMap = sceneDepth.confidenceMap

        // 获取深度图的尺寸
        let width = CVPixelBufferGetWidth(depthMap)
        let height = CVPixelBufferGetHeight(depthMap)

        CVPixelBufferLockBaseAddress(depthMap, .readOnly)
        CVPixelBufferLockBaseAddress(confidenceMap, .readOnly)
        
        defer {
            CVPixelBufferUnlockBaseAddress(depthMap, .readOnly)
            CVPixelBufferUnlockBaseAddress(confidenceMap, .readOnly)
        }
        
        guard let depthData = CVPixelBufferGetBaseAddress(depthMap)?.assumingMemoryBound(to: Float32.self),
              let confidenceData = CVPixelBufferGetBaseAddress(confidenceMap)?.assumingMemoryBound(to: UInt8.self) else {
            print("❌ Failed to access depth/confidence data")
            return
        }
        
        var points: [simd_float3] = []
        let camera = frame.camera
        let intrinsics = camera.intrinsics
        let transform = camera.transform
        
        // 采样步长，避免处理过多点
        let step = max(1, min(width, height) / 100)
        
        for y in stride(from: 0, to: height, by: step) {
            for x in stride(from: 0, to: width, by: step) {
                let index = y * width + x
                let confidence = confidenceData[index]
                
                // 只处理高置信度的点
                guard confidence >= 2 else { continue } // ARConfidenceLevel.high = 2
                
                let depth = depthData[index]
                guard depth > 0 && depth < 10.0 else { continue } // 限制深度范围
                
                // 将像素坐标转换为相机坐标
                let pixelX = Float(x)
                let pixelY = Float(y)
                
                let cameraX = (pixelX - intrinsics[2][0]) / intrinsics[0][0] * depth
                let cameraY = (pixelY - intrinsics[2][1]) / intrinsics[1][1] * depth
                let cameraZ = -depth // 相机坐标系中Z轴向前为负
                
                let cameraPoint = simd_float4(cameraX, cameraY, cameraZ, 1.0)
                
                // 转换到世界坐标系
                let worldPoint = transform * cameraPoint
                points.append(simd_float3(worldPoint.x, worldPoint.y, worldPoint.z))
            }
        }
        
        cachedPointCloud = points
        print("📊 Updated point cloud: \(points.count) points")
    }
}

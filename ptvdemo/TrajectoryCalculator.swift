//
//  TrajectoryCalculator.swift
//  ptvdemo
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import ARKit
import simd

// MARK: - 轨迹计算协议（便于后续替换算法）
protocol TrajectoryCalculating {
    func calculateTrajectory(from start: simd_float3, to end: simd_float3, using arView: ARSCNView) async -> TrajectoryResult
}

// MARK: - 轨迹结果
struct TrajectoryResult {
    let points: [simd_float3]
    let success: Bool
    let message: String
    let metadata: TrajectoryMetadata
}

struct TrajectoryMetadata {
    let samplePointsCount: Int
    let averageSlope: simd_float3
    let calculationTime: TimeInterval
    let algorithm: String
}

// MARK: - 简化版轨迹计算器
class SimpleTrajectoryCalculator: TrajectoryCalculating {
    
    // MARK: - 配置参数
    private struct Config {
        static let sampleCount = 7  // 球到洞之间的采样点数量
        static let slopeBias = Float(0.3)  // 坡度影响系数
        static let trajectoryResolution = 20  // 最终轨迹点数量
        static let raycastHeight = Float(1.0)  // raycast起始高度
        static let maxCalculationTime = TimeInterval(2.0)  // 最大计算时间
    }
    
    func calculateTrajectory(from start: simd_float3, to end: simd_float3, using arView: ARSCNView) async -> TrajectoryResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        print("🧮 开始计算轨迹: \(start) -> \(end)")
        
        // 1. 基础验证
        let distance = simd_distance(start, end)
        guard distance > 0.5 && distance < 15.0 else {
            return TrajectoryResult(
                points: [],
                success: false,
                message: "距离不合适: \(String(format: "%.1f", distance))m",
                metadata: TrajectoryMetadata(
                    samplePointsCount: 0,
                    averageSlope: simd_float3(0, 0, 0),
                    calculationTime: CFAbsoluteTimeGetCurrent() - startTime,
                    algorithm: "SimpleTrajectoryCalculator"
                )
            )
        }
        
        // 2. 沿直线采样地面高度
        let samplePoints = await sampleGroundHeights(from: start, to: end, using: arView)
        
        guard samplePoints.count >= 3 else {
            return TrajectoryResult(
                points: generateFallbackTrajectory(from: start, to: end),
                success: false,
                message: "地面采样不足，使用直线轨迹",
                metadata: TrajectoryMetadata(
                    samplePointsCount: samplePoints.count,
                    averageSlope: simd_float3(0, 0, 0),
                    calculationTime: CFAbsoluteTimeGetCurrent() - startTime,
                    algorithm: "SimpleTrajectoryCalculator-Fallback"
                )
            )
        }
        
        // 3. 计算平均坡度
        let averageSlope = calculateAverageSlope(samplePoints)
        print("📐 平均坡度: \(averageSlope)")
        
        // 4. 生成带坡度影响的轨迹
        let trajectoryPoints = generateSlopeBiasedTrajectory(
            from: start,
            to: end,
            slope: averageSlope,
            samplePoints: samplePoints
        )
        
        let calculationTime = CFAbsoluteTimeGetCurrent() - startTime
        print("⏱️ 轨迹计算完成，耗时: \(String(format: "%.3f", calculationTime))s")
        
        return TrajectoryResult(
            points: trajectoryPoints,
            success: true,
            message: "轨迹计算成功",
            metadata: TrajectoryMetadata(
                samplePointsCount: samplePoints.count,
                averageSlope: averageSlope,
                calculationTime: calculationTime,
                algorithm: "SimpleTrajectoryCalculator"
            )
        )
    }
    
    // MARK: - 私有方法
    
    /// 沿球到洞的直线采样地面高度
    private func sampleGroundHeights(from start: simd_float3, to end: simd_float3, using arView: ARSCNView) async -> [simd_float3] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                var samplePoints: [simd_float3] = []
                
                for i in 0..<Config.sampleCount {
                    let t = Float(i) / Float(Config.sampleCount - 1)
                    let samplePos = start + (end - start) * t
                    
                    // 从上方向下raycast找地面
                    let rayStart = samplePos + simd_float3(0, Config.raycastHeight, 0)
                    let rayEnd = samplePos - simd_float3(0, Config.raycastHeight, 0)
                    
                    if let groundHit = self.raycastToGround(from: rayStart, to: rayEnd, using: arView) {
                        samplePoints.append(groundHit)
                    }
                }
                
                print("🎯 地面采样完成: \(samplePoints.count)/\(Config.sampleCount) 个点")
                continuation.resume(returning: samplePoints)
            }
        }
    }
    
    /// Raycast到地面
    private func raycastToGround(from start: simd_float3, to end: simd_float3, using arView: ARSCNView) -> simd_float3? {
        let direction = simd_normalize(end - start)
        let origin = start
        
        // 创建raycast查询
        let query = ARRaycastQuery(
            origin: origin,
            direction: direction,
            allowing: .estimatedPlane,
            alignment: .horizontal
        )
        
        let results = arView.session.raycast(query)
        if let firstResult = results.first {
            return simd_float3(firstResult.worldTransform.columns.3.x, firstResult.worldTransform.columns.3.y, firstResult.worldTransform.columns.3.z)
        }
        
        return nil
    }
    
    /// 计算平均坡度向量
    private func calculateAverageSlope(_ points: [simd_float3]) -> simd_float3 {
        guard points.count >= 2 else { return simd_float3(0, 0, 0) }
        
        var totalSlope = simd_float3(0, 0, 0)
        var slopeCount = 0
        
        for i in 0..<(points.count - 1) {
            let current = points[i]
            let next = points[i + 1]
            
            let deltaXZ = simd_float2(next.x - current.x, next.z - current.z)
            let deltaY = next.y - current.y
            let horizontalDistance = simd_length(deltaXZ)
            
            if horizontalDistance > 0.01 { // 避免除零
                let slopeDirection = simd_normalize(deltaXZ)
                let slopeMagnitude = deltaY / horizontalDistance
                
                totalSlope += simd_float3(
                    slopeDirection.x * slopeMagnitude,
                    slopeMagnitude,
                    slopeDirection.y * slopeMagnitude
                )
                slopeCount += 1
            }
        }
        
        return slopeCount > 0 ? totalSlope / Float(slopeCount) : simd_float3(0, 0, 0)
    }
    
    /// 生成带坡度影响的轨迹
    private func generateSlopeBiasedTrajectory(from start: simd_float3, to end: simd_float3, slope: simd_float3, samplePoints: [simd_float3]) -> [simd_float3] {
        var trajectoryPoints: [simd_float3] = []
        
        // 计算下坡方向（在XZ平面上）
        let slopeXZ = simd_float2(slope.x, slope.z)
        let slopeDirection = simd_length(slopeXZ) > 0.001 ? simd_normalize(slopeXZ) : simd_float2(0, 0)
        
        for i in 0..<Config.trajectoryResolution {
            let t = Float(i) / Float(Config.trajectoryResolution - 1)
            
            // 基础直线位置
            let basePos = start + (end - start) * t
            
            // 坡度偏移（中间部分偏移最大）
            let biasStrength = sin(t * .pi) * Config.slopeBias  // 抛物线形状
            let biasOffset = simd_float3(
                slopeDirection.x * biasStrength,
                0,
                slopeDirection.y * biasStrength
            )
            
            // 计算最终位置并调整高度
            var finalPos = basePos + biasOffset
            finalPos.y = interpolateHeight(at: finalPos, from: samplePoints)
            
            trajectoryPoints.append(finalPos)
        }
        
        return trajectoryPoints
    }
    
    /// 根据采样点插值计算某位置的地面高度
    private func interpolateHeight(at position: simd_float3, from samplePoints: [simd_float3]) -> Float {
        guard !samplePoints.isEmpty else { return position.y }
        
        // 简单方法：找最近的采样点
        var minDistance = Float.greatestFiniteMagnitude
        var nearestHeight = position.y
        
        for samplePoint in samplePoints {
            let distance = simd_distance(simd_float2(position.x, position.z), simd_float2(samplePoint.x, samplePoint.z))
            if distance < minDistance {
                minDistance = distance
                nearestHeight = samplePoint.y
            }
        }
        
        return nearestHeight
    }
    
    /// 生成备用直线轨迹
    private func generateFallbackTrajectory(from start: simd_float3, to end: simd_float3) -> [simd_float3] {
        var points: [simd_float3] = []
        
        for i in 0..<Config.trajectoryResolution {
            let t = Float(i) / Float(Config.trajectoryResolution - 1)
            points.append(start + (end - start) * t)
        }
        
        return points
    }
}

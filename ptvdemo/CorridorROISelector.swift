//
//  CorridorROISelector.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

import Foundation
import simd

// 2) ROI 选取
final class CorridorROISelector {
    
    func selectCorridor(pointCloud: [simd_float3],
                        ball: simd_float3,
                        hole: simd_float3,
                        width: Float = 0.45) -> [simd_float3] {
        
        let distance = simd_length(hole - ball)
        print("🎯 Selecting corridor: distance=\(distance)m, width=\(width)m")
        
        // 球到洞的方向向量
        let direction = simd_normalize(hole - ball)
        
        // 计算垂直于方向的向量（在XZ平面内）
        let perpendicular = simd_normalize(simd_float3(-direction.z, 0, direction.x))
        
        // ROI范围：长度 = 距离 ±0.5m
        let extraLength: Float = 0.5
        let startPoint = ball - direction * extraLength
        let endPoint = hole + direction * extraLength
        let totalLength = distance + 2 * extraLength
        
        var selectedPoints: [simd_float3] = []
        
        for point in pointCloud {
            // 计算点到球-洞连线的投影
            let toBall = point - ball
            let projectionLength = simd_dot(toBall, direction)
            
            // 检查是否在长度范围内
            guard projectionLength >= -extraLength && projectionLength <= distance + extraLength else {
                continue
            }
            
            // 计算点到连线的距离
            let projectionPoint = ball + direction * projectionLength
            let distanceToLine = simd_length(point - projectionPoint)
            
            // 检查是否在宽度范围内
            guard distanceToLine <= width / 2 else {
                continue
            }
            
            selectedPoints.append(point)
        }
        
        print("📐 ROI selected: \(selectedPoints.count) points from \(pointCloud.count) total")
        return selectedPoints
    }
}

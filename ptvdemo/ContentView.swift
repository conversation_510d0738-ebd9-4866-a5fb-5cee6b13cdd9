//
//  ContentView.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ContentView.swift
import SwiftUI

struct ContentView: View {
    @State private var instruction: String = "点击标记球位置"
    @State private var showHUD: Bool = true
    @State private var controllerRef = ARControllerRef()

    var body: some View {
        ZStack(alignment: .topLeading) {
            ARViewContainer(instruction: $instruction, controllerRef: $controllerRef)
                .ignoresSafeArea()

            if showHUD {
                VStack(alignment: .leading, spacing: 10) {
                    Text(instruction)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.black.opacity(0.68))
                        .clipShape(RoundedRectangle(cornerRadius: 10, style: .continuous))
                    HStack(spacing: 10) {
                        Button {
                            controllerRef.controller?.toggleFeaturePoints()
                        } label: {
                            Label("特征点", systemImage: "circle.grid.2x1")
                                .font(.system(size: 14, weight: .medium))
                                .padding(8)
                                .background(Color.gray.opacity(0.55))
                                .foregroundColor(.white)
                                .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                        }
                        Button {
                            controllerRef.controller?.toggleWorldOrigin()
                        } label: {
                            Label("原点", systemImage: "plus.slash.minus")
                                .font(.system(size: 14, weight: .medium))
                                .padding(8)
                                .background(Color.gray.opacity(0.55))
                                .foregroundColor(.white)
                                .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                        }
                    }
                }
                .padding(.top, 14)
                .padding(.leading, 14)
            }

            VStack { Spacer() }

            VStack {
                Spacer()
                HStack(spacing: 12) {
                    Button {
                        controllerRef.controller?.resetAll()
                        instruction = "点击标记球位置"
                    } label: {
                        Label("重置", systemImage: "arrow.counterclockwise")
                            .padding(.horizontal, 16).padding(.vertical, 10)
                            .background(Color.red.opacity(0.85))
                            .foregroundColor(.white)
                            .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
                    }
                    Button { withAnimation { showHUD.toggle() } } label: {
                        Label(showHUD ? "隐藏HUD" : "显示HUD", systemImage: showHUD ? "eye.slash" : "eye")
                            .padding(.horizontal, 16).padding(.vertical, 10)
                            .background(Color.blue.opacity(0.85))
                            .foregroundColor(.white)
                            .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
                    }
                }
                .padding(.bottom, 22)
            }
            .padding(.horizontal, 16)
        }
    }
}

final class ARControllerRef {
    var controller: ARController? // 改为强引用
}

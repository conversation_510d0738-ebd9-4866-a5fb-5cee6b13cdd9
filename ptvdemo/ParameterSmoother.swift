//
//  ParameterSmoother.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

import Foundation

final class ParameterSmoother {
    private let alpha: Float
    private var previousPlane: PlaneEquation?
    
    init(alpha: Float = 0.25) {
        self.alpha = alpha
        print("🔧 ParameterSmoother initialized with alpha=\(alpha)")
    }
    
    func smooth(_ plane: PlaneEquation) -> PlaneEquation {
        guard let prev = previousPlane else {
            // 第一次调用，直接返回
            previousPlane = plane
            print("📊 First plane, no smoothing applied")
            return plane
        }
        
        // EMA平滑：new = alpha * current + (1 - alpha) * previous
        let smoothedA = alpha * plane.a + (1 - alpha) * prev.a
        let smoothedB = alpha * plane.b + (1 - alpha) * prev.b
        let smoothedC = alpha * plane.c + (1 - alpha) * prev.c
        
        let smoothedPlane = PlaneEquation(a: smoothedA, b: smoothedB, c: smoothedC)
        previousPlane = smoothedPlane
        
        print("🔄 Plane smoothed: (\(prev.a), \(prev.b), \(prev.c)) -> (\(smoothedA), \(smoothedB), \(smoothedC))")
        
        return smoothedPlane
    }
    
    func reset() {
        previousPlane = nil
        print("🔄 ParameterSmoother reset")
    }
}

//
//  ARController.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARController.swift
import Foundation
import ARKit
import SceneKit

final class ARController: NSObject, ARSCNViewDelegate, ARSessionDelegate {
    private let arView: ARSCNView
    private let setInstruction: (String) -> Void

    private var ballAnchor: ARAnchor?
    private var holeAnchor: ARAnchor?
    private var ballNode: SCNNode?
    private var holeNode: SCNNode?

    private var showPoints = false
    private var showOrigin = false

    // Stage 2: 轨迹计算模块
    private let lidar = LiDARManager()
    private let roiSel = CorridorROISelector()
    private let analyzer = SlopeAnalyzer()
    private let smoother = ParameterSmoother()
    private var trajNode: SCNNode?

    init(arView: ARSCNView, instructionCallback: @escaping (String) -> Void) {
        self.arView = arView
        self.setInstruction = instructionCallback
        super.init()
        self.arView.delegate = self
        self.arView.session.delegate = self
        print("🔧 ARController initialized")
    }

    func start() {
        print("🚀 ARController starting...")

        // 检查设备支持
        guard ARWorldTrackingConfiguration.isSupported else {
            print("❌ ARWorldTrackingConfiguration not supported")
            setInstruction("设备不支持AR功能")
            return
        }
        print("✅ ARWorldTrackingConfiguration is supported")

        configureSession()
        setInstruction("点击标记球位置")
        print("📱 Initial instruction set")
    }

    private func configureSession() {
        print("⚙️ Configuring AR session...")

        let cfg = ARWorldTrackingConfiguration()
        cfg.worldAlignment = .gravity
        cfg.planeDetection = [.horizontal] // 基线，便于 raycast
        print("📐 Plane detection set to horizontal")

        if ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh) {
            cfg.sceneReconstruction = .mesh // LiDAR 机型可实时扫描
            print("🔍 LiDAR scene reconstruction enabled")
        } else {
            print("⚠️ LiDAR scene reconstruction not supported")
        }

        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            cfg.frameSemantics.insert([.sceneDepth, .smoothedSceneDepth])
            print("📊 Scene depth enabled")
        } else {
            print("⚠️ Scene depth not supported")
        }

        print("🎬 Starting AR session...")
        arView.session.run(cfg, options: [.resetTracking, .removeExistingAnchors])

        // 配置LiDAR管理器
        lidar.configure(session: arView.session)
    }

    // MARK: - Gestures
    @objc func handleTap(_ g: UITapGestureRecognizer) {
        print("�🚨🚨 ARController.handleTap CALLED! 🚨🚨🚨")
        print("�👆 Tap detected at location: \(g.location(in: arView))")

        let pt = g.location(in: arView)
        // Build a raycast query from the screen point
        guard let query = arView.raycastQuery(from: pt, allowing: .estimatedPlane, alignment: .any) else {
            print("❌ Failed to create raycast query")
            setInstruction("请在可见地面区域点击")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }
        print("✅ Raycast query created successfully")

        // Execute the raycast
        let results = arView.session.raycast(query)
        print("🎯 Raycast results count: \(results.count)")

        guard let r = results.first else {
            print("❌ No raycast results found")
            setInstruction("未检测到平面，请缓慢移动手机扫描地面")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }

        let position = simd_make_float3(r.worldTransform.columns.3)
        print("📍 Hit position: \(position)")

        if ballAnchor == nil {
            print("🟢 Placing ball...")
            placeBall(at: position, transform: r.worldTransform)
            setInstruction("点击标记球洞位置")
        } else if holeAnchor == nil {
            print("🔴 Placing hole...")
            placeHole(at: position, transform: r.worldTransform)
            setInstruction("标记完成（再次点击可重置）")
            // Stage 2: 第二次点击后触发轨迹计算
            computeAndRender()
        } else {
            print("🔄 Resetting all...")
            resetAll()
        }
    }

    func resetAll() {
        if let a = ballAnchor { arView.session.remove(anchor: a) }
        if let a = holeAnchor { arView.session.remove(anchor: a) }
        ballAnchor = nil; holeAnchor = nil
        ballNode?.removeFromParentNode(); ballNode = nil
        holeNode?.removeFromParentNode(); holeNode = nil
        trajNode?.removeFromParentNode(); trajNode = nil
        smoother.reset()
        setInstruction("点击标记球位置")
    }

    // MARK: - Debug toggles
    func toggleFeaturePoints() {
        showPoints.toggle()
        if showPoints { arView.debugOptions.insert(.showFeaturePoints) }
        else { arView.debugOptions.remove(.showFeaturePoints) }
    }

    func toggleWorldOrigin() {
        showOrigin.toggle()
        if showOrigin { arView.debugOptions.insert(.showWorldOrigin) }
        else { arView.debugOptions.remove(.showWorldOrigin) }
    }

    // MARK: - Marker placement
    private func placeBall(at position: simd_float3, transform: simd_float4x4) {
        ballAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: ballAnchor!)
        let node = makeSphere(radius: 0.03, color: .systemGreen)
        node.position = SCNVector3(position.x, position.y + 0.03, position.z)
        arView.scene.rootNode.addChildNode(node)
        ballNode = node
    }

    private func placeHole(at position: simd_float3, transform: simd_float4x4) {
        holeAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: holeAnchor!)
        let node = makeRing(outer: 0.11, tube: 0.008, color: .systemRed)
        node.position = SCNVector3(position.x, position.y + 0.002, position.z)
        node.eulerAngles.x = -.pi/2 // 平躺在地面上
        arView.scene.rootNode.addChildNode(node)
        holeNode = node
    }

    // Simple marker geometry
    private func makeSphere(radius: CGFloat, color: UIColor) -> SCNNode {
        let geo = SCNSphere(radius: radius)
        let m = SCNMaterial(); m.diffuse.contents = color; m.lightingModel = .physicallyBased
        geo.materials = [m]
        return SCNNode(geometry: geo)
    }

    private func makeRing(outer: CGFloat, tube: CGFloat, color: UIColor) -> SCNNode {
        let geo = SCNTorus(ringRadius: outer, pipeRadius: tube)
        let m = SCNMaterial(); m.diffuse.contents = color; m.emission.contents = color.withAlphaComponent(0.6); m.lightingModel = .constant
        geo.materials = [m]
        return SCNNode(geometry: geo)
    }

    // MARK: - Stage 2: 轨迹计算
    private func computeAndRender() {
        guard let ballT = ballAnchor?.transform, let holeT = holeAnchor?.transform else { return }
        let ball = simd_make_float3(ballT.columns.3)
        let hole = simd_make_float3(holeT.columns.3)

        print("🎯 Starting trajectory computation...")
        print("⚽ Ball position: \(ball)")
        print("🕳️ Hole position: \(hole)")

        // 1. 取点云 & ROI
        let cloud = lidar.currentPointCloud
        print("☁️ Point cloud size: \(cloud.count)")

        let corridor = roiSel.selectCorridor(pointCloud: cloud, ball: ball, hole: hole, width: 0.45)
        guard corridor.count >= 20 else {
            setInstruction("点云不足，请慢慢扫过球与洞路径")
            return
        }

        // 2. 二次拟合 + 平滑
        guard let rough = analyzer.fitPlane(points: corridor) else {
            setInstruction("平面拟合失败")
            return
        }

        let tight = corridor.filter { p in
            abs(p.y - rough.heightAt(x: p.x, z: p.z)) < 0.008
        }

        guard let plane0 = analyzer.fitPlane(points: tight.isEmpty ? corridor : tight) else {
            setInstruction("平面拟合失败")
            return
        }

        let plane = smoother.smooth(plane0)

        print("✅ Trajectory computation completed (rendering not implemented yet)")
        setInstruction("轨迹计算完成（渲染待实现）")
    }

    // MARK: - ARSessionDelegate
    func session(_ session: ARSession, didAdd anchors: [ARAnchor]) {
        print("⚓ Anchors added: \(anchors.count)")
    }

    func session(_ session: ARSession, didUpdate frame: ARFrame) {
        // 更新点云数据
        lidar.updatePointCloud(from: frame)
    }

    func session(_ session: ARSession, didUpdate anchors: [ARAnchor]) {
        // print("🔄 Anchors updated: \(anchors.count)")
    }

    func session(_ session: ARSession, didRemove anchors: [ARAnchor]) {
        print("🗑️ Anchors removed: \(anchors.count)")
    }

    func session(_ session: ARSession, didFailWithError error: Error) {
        print("💥 AR Session failed with error: \(error.localizedDescription)")
        setInstruction("AR会话出错: \(error.localizedDescription)")
    }

    func sessionWasInterrupted(_ session: ARSession) {
        print("⏸️ AR Session was interrupted")
        setInstruction("AR会话被中断")
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        print("▶️ AR Session interruption ended")
        setInstruction("AR会话恢复，点击标记球位置")
    }

    func session(_ session: ARSession, cameraDidChangeTrackingState camera: ARCamera) {
        let state = camera.trackingState
        print("📷 Camera tracking state: \(state)")

        switch state {
        case .normal:
            print("✅ Tracking normal")
            // 不改变指令，保持当前状态
        case .notAvailable:
            print("❌ Tracking not available")
            setInstruction("AR跟踪不可用")
        case .limited(let reason):
            print("⚠️ Tracking limited: \(reason)")
            switch reason {
            case .excessiveMotion:
                setInstruction("移动过快，请缓慢移动设备")
            case .insufficientFeatures:
                setInstruction("特征点不足，请对准有纹理的表面")
            case .initializing:
                setInstruction("AR正在初始化...")
            case .relocalizing:
                setInstruction("AR正在重新定位...")
            @unknown default:
                setInstruction("AR跟踪受限")
            }
        }
    }
}

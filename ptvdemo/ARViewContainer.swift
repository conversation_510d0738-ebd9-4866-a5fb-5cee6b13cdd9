//
//  ARViewContainer.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARViewContainer.swift
import SwiftUI
import ARKit
import SceneKit

struct ARViewContainer: UIViewRepresentable {
    @Binding var instruction: String
    @Binding var controllerRef: ARControllerRef
    @Binding var target: TargetKind
    @Binding var reticlePx: CGFloat
    @Binding var reticleLocked: Bool

    func makeCoordinator() -> Coordinator { Coordinator(self) }

    func makeUIView(context: Context) -> ARSCNView {
        print("🏗️ Creating ARSCNView...")

        let view = ARSCNView(frame: .zero)
        view.isUserInteractionEnabled = true
        view.autoenablesDefaultLighting = true
        view.automaticallyUpdatesLighting = true
        view.scene = SCNScene()
        print("📺 ARSCNView configured")

        let controller = ARController(
            arView: view,
            instructionCallback: { msg in
                print("📝 Instruction update: \(msg)")
                DispatchQueue.main.async { instruction = msg }
            },
            reticleCallback: { px, locked in
                print("🎯 Reticle update -> px: \(px), locked: \(locked)")
                DispatchQueue.main.async {
                    // Clamp for UI aesthetics
                    reticlePx = max(36, min(px, 180))
                    reticleLocked = locked
                }
            }
        )
        controllerRef.controller = controller
        context.coordinator.controller = controller
        print("🎮 ARController created and linked")
        print("🔗 ControllerRef.controller: \(controllerRef.controller != nil ? "✅" : "❌")")
        print("🔗 Coordinator.controller: \(context.coordinator.controller != nil ? "✅" : "❌")")

        controller.start()

        let tap = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleTap(_:)))
        view.addGestureRecognizer(tap)
        print("👆 Tap gesture recognizer added")

        return view
    }

    func updateUIView(_ uiView: ARSCNView, context: Context) {
        context.coordinator.controller?.setTarget(kind: target)
    }

    final class Coordinator: NSObject {
        var parent: ARViewContainer
        var controller: ARController?
        init(_ parent: ARViewContainer) {
            self.parent = parent
            print("🤝 Coordinator initialized")
        }
        @objc func handleTap(_ g: UITapGestureRecognizer) {
            print("🎯 Coordinator received tap, forwarding to controller...")
            if let controller = controller {
                print("✅ Controller exists, calling handleCenterTap")
                controller.handleCenterTap()
            } else {
                print("❌ Controller is nil!")
            }
        }
    }
}

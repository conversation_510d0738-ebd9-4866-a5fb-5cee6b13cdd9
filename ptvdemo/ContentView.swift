//
//  ContentView.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ContentView.swift
import SwiftUI

struct ContentView: View {
    @State private var instruction: String = "请选择"球"模式并瞄准球位置"
    @State private var showHUD: Bool = true
    @State private var controllerRef = ARControllerRef()
    @State private var target: TargetKind = .ball
    @State private var reticlePx: CGFloat = 64
    @State private var reticleLocked: Bool = false

    var body: some View {
        ZStack(alignment: .topLeading) {
            ARViewContainer(instruction: $instruction, controllerRef: $controllerRef, target: $target, reticlePx: $reticlePx, reticleLocked: $reticleLocked)
                .ignoresSafeArea()

            if showHUD {
                VStack(alignment: .leading, spacing: 10) {
                    Picker("", selection: $target) {
                        Text("球").tag(TargetKind.ball)
                        Text("洞").tag(TargetKind.hole)
                    }
                    .pickerStyle(.segmented)
                    .frame(width: 160)

                    Text(instruction)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.black.opacity(0.68))
                        .clipShape(RoundedRectangle(cornerRadius: 10, style: .continuous))
                        .allowsHitTesting(false)
                    HStack(spacing: 10) {
                        Button {
                            controllerRef.controller?.toggleFeaturePoints()
                        } label: {
                            Label("特征点", systemImage: "circle.grid.2x1")
                                .font(.system(size: 14, weight: .medium))
                                .padding(8)
                                .background(Color.gray.opacity(0.55))
                                .foregroundColor(.white)
                                .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                        }
                        Button {
                            controllerRef.controller?.toggleWorldOrigin()
                        } label: {
                            Label("原点", systemImage: "plus.slash.minus")
                                .font(.system(size: 14, weight: .medium))
                                .padding(8)
                                .background(Color.gray.opacity(0.55))
                                .foregroundColor(.white)
                                .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                        }
                    }
                }
                .padding(.top, 14)
                .padding(.leading, 14)
            }

            // Center reticle overlay (does not block touches)
            ReticleView(size: reticlePx, locked: reticleLocked)
                .allowsHitTesting(false)

            VStack { Spacer() }

            VStack {
                Spacer()
                HStack(spacing: 12) {
                    Button {
                        controllerRef.controller?.resetAll()
                        target = .ball  // 重置到球模式
                    } label: {
                        Label("重置", systemImage: "arrow.counterclockwise")
                            .padding(.horizontal, 16).padding(.vertical, 10)
                            .background(Color.red.opacity(0.85))
                            .foregroundColor(.white)
                            .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
                    }
                    Button { withAnimation { showHUD.toggle() } } label: {
                        Label(showHUD ? "隐藏HUD" : "显示HUD", systemImage: showHUD ? "eye.slash" : "eye")
                            .padding(.horizontal, 16).padding(.vertical, 10)
                            .background(Color.blue.opacity(0.85))
                            .foregroundColor(.white)
                            .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
                    }
                }
                .padding(.bottom, 22)
            }
            .padding(.horizontal, 16)
        }
    }
}

final class ARControllerRef {
    var controller: ARController? // 改为强引用
}

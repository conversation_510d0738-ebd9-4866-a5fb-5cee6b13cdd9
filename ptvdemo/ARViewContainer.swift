//
//  ARViewContainer.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARViewContainer.swift
import SwiftUI
import ARKit
import SceneKit

struct ARViewContainer: UIViewRepresentable {
    @Binding var instruction: String
    @Binding var controllerRef: ARControllerRef

    func makeCoordinator() -> Coordinator { Coordinator(self) }

    func makeUIView(context: Context) -> ARSCNView {
        let view = ARSCNView(frame: .zero)
        view.autoenablesDefaultLighting = true
        view.automaticallyUpdatesLighting = true
        view.scene = SCNScene()
        let controller = ARController(arView: view) { msg in
            DispatchQueue.main.async { instruction = msg }
        }
        controllerRef.controller = controller
        context.coordinator.controller = controller
        controller.start()

        let tap = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleTap(_:)))
        view.addGestureRecognizer(tap)
        return view
    }

    func updateUIView(_ uiView: ARSCNView, context: Context) {}

    final class Coordinator: NSObject {
        var parent: ARViewContainer
        weak var controller: ARController?
        init(_ parent: ARViewContainer) { self.parent = parent }
        @objc func handleTap(_ g: UITapGestureRecognizer) { controller?.handleTap(g) }
    }
}

//
//  ARController.swift
//  ptvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/5.
//

// MARK: - ARController.swift
import Foundation
import ARKit
import SceneKit

// 定义目标类型枚举
enum TargetKind: String, CaseIterable {
    case ball = "球"
    case hole = "洞"
}

final class ARController: NSObject, ARSCNViewDelegate, ARSessionDelegate {
    private let arView: ARSCNView
    private let setInstruction: (String) -> Void

    private let reticleUpdate: (_ pixels: CGFloat, _ locked: Bool) -> Void

    enum Target { case ball, hole }
    private var currentTarget: Target = .ball

    // Throttle reticle UI updates
    private var lastReticleUpdateTime: CFTimeInterval = 0

    private var ballAnchor: ARAnchor?
    private var holeAnchor: ARAnchor?
    private var ballNode: SCNNode?
    private var holeNode: SCNNode?

    private var showPoints = false
    private var showOrigin = false

    init(arView: ARSCNView,
         instructionCallback: @escaping (String) -> Void,
         reticleCallback: @escaping (_ pixels: CGFloat, _ locked: Bool) -> Void) {
        self.arView = arView
        self.setInstruction = instructionCallback
        self.reticleUpdate = reticleCallback
        super.init()
        self.arView.delegate = self
        self.arView.session.delegate = self
        print("🔧 ARController initialized")
    }

    func start() {
        print("🚀 ARController starting...")

        // 检查设备支持
        guard ARWorldTrackingConfiguration.isSupported else {
            print("❌ ARWorldTrackingConfiguration not supported")
            setInstruction("设备不支持AR功能")
            return
        }
        print("✅ ARWorldTrackingConfiguration is supported")

        configureSession()
        setInstruction("请选择\"球\"模式并瞄准球位置")
        print("📱 Initial instruction set")
    }

    // MARK: - Target selection & center-tap placement
    func setTarget(kind: TargetKind) {
        currentTarget = (kind == .ball) ? .ball : .hole
        print("🎯 Target set to: \(kind == .ball ? \"球\" : \"洞\")")
    }

    /// Use the center reticle to place marker, keeping UX consistent
    @objc func handleCenterTap() {
        guard let hit = centerRaycastHit() else {
            setInstruction("未检测到平面，请缓慢移动手机扫描地面")
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
            return
        }
        let position = simd_make_float3(hit.worldTransform.columns.3)
        switch currentTarget {
        case .ball:
            print("🟢 Center placing ball at: \(position)")
            placeBall(at: position, transform: hit.worldTransform)
            setInstruction("切换到“洞”并点击以标定洞位")
        case .hole:
            print("🔴 Center placing hole at: \(position)")
            placeHole(at: position, transform: hit.worldTransform)
            setInstruction("标定完成（再次点击可重置）")
        }
    }

    private func configureSession() {
        print("⚙️ Configuring AR session...")

        let cfg = ARWorldTrackingConfiguration()
        cfg.worldAlignment = .gravity
        cfg.planeDetection = [.horizontal] // 基线，便于 raycast
        print("📐 Plane detection set to horizontal")

        if ARWorldTrackingConfiguration.supportsSceneReconstruction(.mesh) {
            cfg.sceneReconstruction = .mesh // LiDAR 机型可实时扫描
            print("🔍 LiDAR scene reconstruction enabled")
        } else {
            print("⚠️ LiDAR scene reconstruction not supported")
        }

        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            cfg.frameSemantics.insert([.sceneDepth, .smoothedSceneDepth])
            print("📊 Scene depth enabled")
        } else {
            print("⚠️ Scene depth not supported")
        }

        print("🎬 Starting AR session...")
        arView.session.run(cfg, options: [.resetTracking, .removeExistingAnchors])
    }

    // MARK: - Gestures (使用中央准星点击)
    @objc func handleTap(_ g: UITapGestureRecognizer) {
        print("🎯 Tap detected, using center reticle for placement")
        handleCenterTap()
    }

    func resetAll() {
        print("🔄 Resetting all markers")
        if let a = ballAnchor { arView.session.remove(anchor: a) }
        if let a = holeAnchor { arView.session.remove(anchor: a) }
        ballAnchor = nil; holeAnchor = nil
        ballNode?.removeFromParentNode(); ballNode = nil
        holeNode?.removeFromParentNode(); holeNode = nil

        // 重置到球模式
        currentTarget = .ball
        setInstruction("请选择\"球\"模式并瞄准球位置")
    }

    // MARK: - Debug toggles
    func toggleFeaturePoints() {
        showPoints.toggle()
        if showPoints { arView.debugOptions.insert(.showFeaturePoints) }
        else { arView.debugOptions.remove(.showFeaturePoints) }
    }

    func toggleWorldOrigin() {
        showOrigin.toggle()
        if showOrigin { arView.debugOptions.insert(.showWorldOrigin) }
        else { arView.debugOptions.remove(.showWorldOrigin) }
    }

    // MARK: - Reticle helpers
    private func centerRaycastHit() -> ARRaycastResult? {
        let size = arView.bounds.size
        let center = CGPoint(x: size.width * 0.5, y: size.height * 0.5)
        guard let query = arView.raycastQuery(from: center, allowing: .estimatedPlane, alignment: .horizontal) else { return nil }
        return arView.session.raycast(query).first
    }

    private func pixelDiameter(for hit: ARRaycastResult, frame: ARFrame) -> CGFloat {
        // Real-world diameters (meters)
        let realDiameter: Float = (currentTarget == .ball) ? 0.0427 : 0.108
        let w = hit.worldTransform.columns.3
        // World -> Camera space
        let worldToCam = simd_inverse(frame.camera.transform)
        let camP = worldToCam * simd_float4(w.x, w.y, w.z, 1)
        let z = max(0.01, abs(camP.z)) // ARKit forward is typically -Z; use abs and clamp
        let fx = frame.camera.intrinsics.columns.0.x // focal length in pixels
        let px = fx * (realDiameter / z)
        return CGFloat(px)
    }

    // MARK: - Marker placement
    private func placeBall(at position: simd_float3, transform: simd_float4x4) {
        ballAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: ballAnchor!)
        let node = makeSphere(radius: 0.03, color: .systemGreen)
        node.position = SCNVector3(position.x, position.y + 0.03, position.z)
        arView.scene.rootNode.addChildNode(node)
        ballNode = node
    }

    private func placeHole(at position: simd_float3, transform: simd_float4x4) {
        holeAnchor = ARAnchor(transform: transform)
        arView.session.add(anchor: holeAnchor!)
        let node = makeRing(outer: 0.11, tube: 0.008, color: .systemRed)
        node.position = SCNVector3(position.x, position.y + 0.002, position.z)
        node.eulerAngles.x = -.pi/2 // 平躺在地面上
        arView.scene.rootNode.addChildNode(node)
        holeNode = node
    }

    // Simple marker geometry
    private func makeSphere(radius: CGFloat, color: UIColor) -> SCNNode {
        let geo = SCNSphere(radius: radius)
        let m = SCNMaterial(); m.diffuse.contents = color; m.lightingModel = .physicallyBased
        geo.materials = [m]
        return SCNNode(geometry: geo)
    }

    private func makeRing(outer: CGFloat, tube: CGFloat, color: UIColor) -> SCNNode {
        let geo = SCNTorus(ringRadius: outer, pipeRadius: tube)
        let m = SCNMaterial(); m.diffuse.contents = color; m.emission.contents = color.withAlphaComponent(0.6); m.lightingModel = .constant
        geo.materials = [m]
        return SCNNode(geometry: geo)
    }

    // MARK: - ARSessionDelegate
    func session(_ session: ARSession, didAdd anchors: [ARAnchor]) {
        print("⚓ Anchors added: \(anchors.count)")
    }

    func session(_ session: ARSession, didUpdate anchors: [ARAnchor]) {
        // print("🔄 Anchors updated: \(anchors.count)")
    }

    func session(_ session: ARSession, didRemove anchors: [ARAnchor]) {
        print("🗑️ Anchors removed: \(anchors.count)")
    }

    func session(_ session: ARSession, didFailWithError error: Error) {
        print("💥 AR Session failed with error: \(error.localizedDescription)")
        setInstruction("AR会话出错: \(error.localizedDescription)")
    }

    func sessionWasInterrupted(_ session: ARSession) {
        print("⏸️ AR Session was interrupted")
        setInstruction("AR会话被中断")
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        print("▶️ AR Session interruption ended")
        setInstruction("AR会话恢复，点击标记球位置")
    }

    func session(_ session: ARSession, cameraDidChangeTrackingState camera: ARCamera) {
        let state = camera.trackingState
        print("📷 Camera tracking state: \(state)")

        switch state {
        case .normal:
            print("✅ Tracking normal")
            // 不改变指令，保持当前状态
        case .notAvailable:
            print("❌ Tracking not available")
            setInstruction("AR跟踪不可用")
        case .limited(let reason):
            print("⚠️ Tracking limited: \(reason)")
            switch reason {
            case .excessiveMotion:
                setInstruction("移动过快，请缓慢移动设备")
            case .insufficientFeatures:
                setInstruction("特征点不足，请对准有纹理的表面")
            case .initializing:
                setInstruction("AR正在初始化...")
            case .relocalizing:
                setInstruction("AR正在重新定位...")
            @unknown default:
                setInstruction("AR跟踪受限")
            }
        }
    }

    func session(_ session: ARSession, didUpdate frame: ARFrame) {
        let now = CACurrentMediaTime()
        guard now - lastReticleUpdateTime > 0.05 else { return } // ~20 Hz
        lastReticleUpdateTime = now
        if let hit = centerRaycastHit() {
            let px = pixelDiameter(for: hit, frame: frame)
            reticleUpdate(px, true)
        } else {
            reticleUpdate(64, false)
        }
    }
}
